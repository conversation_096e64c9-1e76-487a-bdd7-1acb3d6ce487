{"roots": ["flutter_tools"], "packages": [{"name": "flutter_tools", "version": "0.0.0", "dependencies": ["_fe_analyzer_shared", "analyzer", "archive", "args", "async", "boolean_selector", "browser_launcher", "built_collection", "built_value", "clock", "code_builder", "completion", "convert", "coverage", "crypto", "csslib", "dap", "dart_service_protocol_shared", "dds", "dds_service_extensions", "devtools_shared", "dtd", "dwds", "extension_discovery", "fake_async", "ffi", "file", "fixnum", "flutter_template_images", "frontend_server_client", "glob", "graphs", "html", "http", "http_multi_server", "http_parser", "intl", "io", "json_rpc_2", "logging", "matcher", "meta", "mime", "multicast_dns", "mustache_template", "native_assets_builder", "native_assets_cli", "native_stack_traces", "package_config", "path", "petitparser", "platform", "pool", "process", "pub_semver", "shelf", "shelf_packages_handler", "shelf_proxy", "shelf_static", "shelf_web_socket", "source_map_stack_trace", "source_maps", "source_span", "sprintf", "sse", "stack_trace", "standard_message_codec", "stream_channel", "string_scanner", "sync_http", "term_glyph", "test_api", "test_core", "typed_data", "unified_analytics", "usage", "uuid", "vm_service", "vm_service_interface", "vm_snapshot_analysis", "watcher", "web", "web_socket", "web_socket_channel", "webdriver", "webkit_inspection_protocol", "xml", "yaml", "yaml_edit"], "devDependencies": ["checked_yaml", "collection", "file_testing", "js", "json_annotation", "node_preamble", "pubspec_parse", "test"]}, {"name": "test", "version": "1.25.15", "dependencies": ["analyzer", "async", "boolean_selector", "collection", "coverage", "http_multi_server", "io", "js", "matcher", "node_preamble", "package_config", "path", "pool", "shelf", "shelf_packages_handler", "shelf_static", "shelf_web_socket", "source_span", "stack_trace", "stream_channel", "test_api", "test_core", "typed_data", "web_socket_channel", "webkit_inspection_protocol", "yaml"]}, {"name": "node_preamble", "version": "2.0.2", "dependencies": []}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "checked_yaml", "version": "2.0.3", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "file_testing", "version": "3.0.2", "dependencies": ["test"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "yaml_edit", "version": "2.2.2", "dependencies": ["collection", "meta", "source_span", "yaml"]}, {"name": "web_socket", "version": "0.1.6", "dependencies": ["web"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "watcher", "version": "1.1.1", "dependencies": ["async", "path"]}, {"name": "vm_service_interface", "version": "2.0.1", "dependencies": ["vm_service"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "sync_http", "version": "0.3.1", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "sse", "version": "4.1.8", "dependencies": ["async", "collection", "logging", "pool", "shelf", "stream_channel", "web"]}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "source_maps", "version": "0.10.13", "dependencies": ["source_span"]}, {"name": "source_map_stack_trace", "version": "2.1.2", "dependencies": ["path", "source_maps", "stack_trace"]}, {"name": "shelf_proxy", "version": "1.0.4", "dependencies": ["http", "path", "shelf"]}, {"name": "shelf_packages_handler", "version": "3.0.2", "dependencies": ["path", "shelf", "shelf_static"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "json_rpc_2", "version": "4.0.0", "dependencies": ["stack_trace", "stream_channel"]}, {"name": "io", "version": "1.0.5", "dependencies": ["meta", "path", "string_scanner"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "glob", "version": "2.1.3", "dependencies": ["async", "collection", "file", "path", "string_scanner"]}, {"name": "frontend_server_client", "version": "4.0.0", "dependencies": ["async", "path"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "extension_discovery", "version": "2.1.0", "dependencies": ["yaml"]}, {"name": "dtd", "version": "4.0.0", "dependencies": ["dart_service_protocol_shared", "json_rpc_2", "stream_channel", "unified_analytics", "web_socket_channel"]}, {"name": "devtools_shared", "version": "12.0.0", "dependencies": ["args", "collection", "dtd", "extension_discovery", "meta", "path", "shelf", "sse", "vm_service", "web_socket_channel", "webkit_inspection_protocol", "yaml", "yaml_edit"]}, {"name": "dds_service_extensions", "version": "2.0.2", "dependencies": ["dap", "vm_service"]}, {"name": "dart_service_protocol_shared", "version": "0.0.3", "dependencies": ["meta"]}, {"name": "dap", "version": "1.4.0", "dependencies": []}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "built_value", "version": "8.10.1", "dependencies": ["built_collection", "collection", "fixnum", "meta"]}, {"name": "built_collection", "version": "5.1.1", "dependencies": []}, {"name": "browser_launcher", "version": "1.1.3", "dependencies": ["logging", "path", "webkit_inspection_protocol"]}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "analyzer", "version": "7.3.0", "dependencies": ["_fe_analyzer_shared", "collection", "convert", "crypto", "glob", "meta", "package_config", "path", "pub_semver", "source_span", "watcher", "yaml"]}, {"name": "_fe_analyzer_shared", "version": "80.0.0", "dependencies": ["meta"]}, {"name": "standard_message_codec", "version": "0.0.1+4", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "test_core", "version": "0.6.8", "dependencies": ["analyzer", "args", "async", "boolean_selector", "collection", "coverage", "frontend_server_client", "glob", "io", "meta", "package_config", "path", "pool", "source_map_stack_trace", "source_maps", "source_span", "stack_trace", "stream_channel", "test_api", "vm_service", "yaml"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "native_assets_cli", "version": "0.13.0", "dependencies": ["collection", "crypto", "logging", "meta", "pub_semver", "yaml"]}, {"name": "native_assets_builder", "version": "0.13.0", "dependencies": ["collection", "crypto", "file", "graphs", "logging", "meta", "native_assets_cli", "package_config", "pub_semver", "yaml", "yaml_edit"]}, {"name": "graphs", "version": "2.3.2", "dependencies": ["collection"]}, {"name": "unified_analytics", "version": "7.0.2", "dependencies": ["clock", "convert", "file", "http", "meta"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "http_multi_server", "version": "3.2.2", "dependencies": ["async"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "shelf_static", "version": "1.1.3", "dependencies": ["convert", "http_parser", "mime", "path", "shelf"]}, {"name": "shelf_web_socket", "version": "2.0.1", "dependencies": ["shelf", "stream_channel", "web_socket_channel"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "web_socket_channel", "version": "3.0.2", "dependencies": ["async", "crypto", "stream_channel", "web", "web_socket"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "vm_snapshot_analysis", "version": "0.7.6", "dependencies": ["args", "collection", "path"]}, {"name": "shelf", "version": "1.4.2", "dependencies": ["async", "collection", "http_parser", "path", "stack_trace", "stream_channel"]}, {"name": "native_stack_traces", "version": "0.6.0", "dependencies": ["args", "path"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "webkit_inspection_protocol", "version": "1.2.1", "dependencies": ["logging"]}, {"name": "webdriver", "version": "3.1.0", "dependencies": ["matcher", "path", "stack_trace", "sync_http"]}, {"name": "usage", "version": "4.1.1", "dependencies": ["meta", "path"]}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "process", "version": "5.0.3", "dependencies": ["file", "path", "platform"]}, {"name": "package_config", "version": "2.2.0", "dependencies": ["path"]}, {"name": "mustache_template", "version": "2.0.0", "dependencies": []}, {"name": "multicast_dns", "version": "0.3.3", "dependencies": ["meta"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "http", "version": "1.3.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "html", "version": "0.15.5+1", "dependencies": ["csslib", "source_span"]}, {"name": "flutter_template_images", "version": "5.0.0", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "coverage", "version": "1.11.1", "dependencies": ["args", "glob", "logging", "meta", "package_config", "path", "source_maps", "stack_trace", "vm_service"]}, {"name": "completion", "version": "1.0.1", "dependencies": ["args", "logging", "path"]}, {"name": "code_builder", "version": "4.10.1", "dependencies": ["built_collection", "built_value", "collection", "matcher", "meta"]}, {"name": "dwds", "version": "24.3.10", "dependencies": ["async", "built_collection", "built_value", "collection", "crypto", "dds", "file", "http", "http_multi_server", "logging", "meta", "package_config", "path", "pool", "pub_semver", "shelf", "shelf_packages_handler", "shelf_proxy", "shelf_static", "shelf_web_socket", "source_maps", "sse", "stack_trace", "uuid", "vm_service", "vm_service_interface", "web", "web_socket_channel", "webkit_inspection_protocol"]}, {"name": "dds", "version": "5.0.3", "dependencies": ["args", "async", "browser_launcher", "collection", "dap", "dds_service_extensions", "devtools_shared", "dtd", "extension_discovery", "http_multi_server", "json_rpc_2", "meta", "mime", "path", "shelf", "shelf_proxy", "shelf_static", "shelf_web_socket", "sse", "stream_channel", "vm_service", "web_socket_channel"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "archive", "version": "3.6.1", "dependencies": ["crypto", "path"]}], "configVersion": 1}